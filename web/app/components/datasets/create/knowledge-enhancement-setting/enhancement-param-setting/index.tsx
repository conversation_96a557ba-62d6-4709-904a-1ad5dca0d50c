'use client'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Checkbox, Switch } from 'antd'
import type { CheckboxOptionType } from 'antd'

import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
import { useDefaultModel, useModelList } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'

// 公共组件
import Tooltip from '@/app/components/base/tooltip'

type EnhancementConfig = {
  enhanced_enable: boolean
  enhanced_feature: string[]
  knowledge_model: {
    reranking_provider_name: string
    knowledge_model_name: string
  }
}

type Props = {
  type?: string
  value?: Partial<EnhancementConfig>
  onChange: (value: EnhancementConfig) => void
}

const EnhancementParamSetting = forwardRef(({
  type = '',
  value: passValue = {
    enhanced_enable: false,
    enhanced_feature: ['paragraphSummary'],
    knowledge_model: {
      reranking_provider_name: '',
      knowledge_model_name: '',
    },
  },
  onChange,
}: Props, ref) => {
  const { t } = useTranslation()
  const {
    data: embeddingModelList,
    mutate: mutateEmbeddingModelList,
    isValidating: isEmbeddingModelListLoading,
  } = useModelList(ModelTypeEnum.textEmbedding)
  const { data: defaultEmbeddingModel } = useDefaultModel(
    ModelTypeEnum.textEmbedding,
  )

  // 当前配置值-作为默认值
  const [currentValue, setCurrentValue] = useState<EnhancementConfig>({
    enhanced_enable: false, // 知识增强开关
    enhanced_feature: ['paragraphSummary'], // 增强内容 默认选中段落总结
    knowledge_model: {
      reranking_provider_name: defaultEmbeddingModel?.provider.provider || '',
      knowledge_model_name: defaultEmbeddingModel?.model || '',
    },
  })

  // 参数配置初始化
  useEffect(() => {
    if (!passValue.knowledge_model?.reranking_provider_name) {
      setCurrentValue({
        ...currentValue,
        ...passValue,
        knowledge_model: {
          reranking_provider_name: defaultEmbeddingModel?.provider.provider || '',
          knowledge_model_name: defaultEmbeddingModel?.model || '',
        },
      })
    }
    else {
      setCurrentValue({
        ...currentValue,
        ...passValue,
      })
    }
  }, [passValue, defaultEmbeddingModel])

  useImperativeHandle(ref, () => ({
    getConfig() {
      return currentValue
    },
    resetConfig() {
      setCurrentValue({
        enhanced_enable: false, // 知识增强开关
        enhanced_feature: ['paragraphSummary', 'problemGeneration'], // 增强内容
        knowledge_model: {
          reranking_provider_name: defaultEmbeddingModel?.provider.provider || '',
          knowledge_model_name: defaultEmbeddingModel?.model || '',
        },
      })
    },
  }))

  const plainOptions: CheckboxOptionType<string>[] = [
    { label: t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.paragraphSummary'), value: 'paragraphSummary' },
    { label: t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.problemGeneration'), value: 'problemGeneration' },
  ]

  return (
    <div>
      {/* 知识增强 */}
      <div className='flex h-9 items-center'>
        <div className='w-[134px] flex justify-end items-center text-gray-G1 shrink-0 text-S3 leading-H3'>
          <div className='mr-1 truncate' title={t('datasetCreation.stepTwo.knowledgeEnhancement.title')!}>{t('datasetCreation.stepTwo.knowledgeEnhancement.title')}</div>
          <Tooltip
            popupContent={
              <div className="w-[200px]">{t('datasetCreation.stepTwo.knowledgeEnhancement.description')}</div>
            }
          ></Tooltip>
        </div>
        {/* TODO：旧数据=》type === 'edit'；关闭状态；hover时提示：历史知识库不支持打开该功能。 */}
        <Switch
          className='ml-4 shrink-0'
          size='small'
          checked={currentValue.enhanced_enable}
          onChange={(v) => {
            onChange({
              ...currentValue,
              enhanced_enable: v,
            })
          }}
        />
      </div>
      {
        currentValue.enhanced_enable && (
          <>
            {/* 增强内容 */}
            <div className='flex h-9 items-center'>
              <div className='w-[134px] flex justify-end items-center text-gray-G1 shrink-0 text-S3 leading-H3'>
                <i className='text-[#f15b4c] mr-1'>*</i>
                <div className='mr-1 truncate' title={t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.title')!}>
                  {t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.title')}
                </div>
              </div>
              <Checkbox.Group
                options={plainOptions}
                value={currentValue.enhanced_feature}
                onChange={(checkedValue) => {
                  onChange({
                    ...currentValue,
                    enhanced_feature: checkedValue as string[],
                  })
                }}
                className='ml-4 shrink-0 flex items-center'
              />
            </div>
          </>
        )
      }

      {currentValue.enhanced_enable && (
        <>
          {/* 模型选择 */}
          <div className='flex h-9 items-center'>
            <div className='flex justify-end items-center shrink-0 w-[134px]'>
              <div title={t('datasetCreation.stepTwo.knowledgeEnhancement.modelSelect')!} className='mr-1 text-gray-G1 truncate shrink-0 text-S3 leading-H3'>{t('account.modelProvider.modelSelect')}</div>
              <Tooltip
                popupContent={
                  <div className="w-[200px]">{t('account.modelProvider.rerankModel.tip')}</div>
                }
              ></Tooltip>
            </div>

            <ModelSelector
              wrapperClassName='w-full shrink ml-4'
              triggerClassName={'w-full'}
              loading={isEmbeddingModelListLoading}
              readonly={true}
              defaultModel={currentValue && {
                provider: currentValue.knowledge_model.reranking_provider_name,
                model: currentValue.knowledge_model.knowledge_model_name,
              }}
              modelList={embeddingModelList}
              onSelect={(v) => {
                onChange({
                  ...currentValue,
                  knowledge_model: {
                    reranking_provider_name: v.provider,
                    knowledge_model_name: v.model,
                  },
                })
              }}
              onFetch={mutateEmbeddingModelList}
            />
          </div>
        </>
      )}
    </div>
  )
})

EnhancementParamSetting.displayName = 'EnhancementParamSetting'

export default React.memo(EnhancementParamSetting)
