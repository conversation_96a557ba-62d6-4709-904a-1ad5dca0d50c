'use client'
import React, { forwardRef, useImperativeHandle, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Card } from 'antd'
import EnhancementParamSetting from './enhancement-param-setting'
import type { RetrievalConfig } from '@/types/datasets'
import style from '@/app/components/datasets/create/knowledge-enhancement-setting/styles/style.module.scss'

export type KnowledgeEnhanceConfig = RetrievalConfig & {
  enhanced_enable: boolean
  enhanced_feature?: string[]
}

type Props = {
  value?: KnowledgeEnhanceConfig
  disabled?: boolean
  onChange: (value: KnowledgeEnhanceConfig) => void
  type?: 'create' | 'edit'
}

type EnhancementParamSettingRef = {
  resetConfig: () => void
  getConfig: () => KnowledgeEnhanceConfig
}

const KnowledgeEnhancementSetting = forwardRef<EnhancementParamSettingRef, Props>(({
  value: passValue,
  disabled = false,
  onChange,
  type,
}, ref) => {
  const { t } = useTranslation()
  const knowRef = useRef<EnhancementParamSettingRef>(null)

  // 数据预处理
  const value = useMemo(() => {
    if (passValue) {
      if (!passValue.weights) {
        return {
          ...passValue,
        }
      }
      return passValue
    }
    return undefined
  }, [passValue])

  // 重置索引方法
  const resetRetrievalMethod = () => {
    knowRef.current?.resetConfig()
  }

  useImperativeHandle(ref, () => ({
    resetConfig: resetRetrievalMethod,
    getConfig: () => knowRef.current?.getConfig() || value || {},
  }))

  return (
    <div className={style.enhancementSty}>
      <Card className={style.enhancementSetSty}>
        <EnhancementParamSetting
          ref={knowRef}
          value={value}
          onChange={onChange}
          type={type}
          disabled={disabled}
        />
      </Card>
    </div>
  )
})

KnowledgeEnhancementSetting.displayName = 'KnowledgeEnhancementSetting'

export default KnowledgeEnhancementSetting
