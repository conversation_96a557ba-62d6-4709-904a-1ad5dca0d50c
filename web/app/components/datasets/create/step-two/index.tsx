'use client'
import React, { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useAsyncEffect, useBoolean, useDebounceEffect } from 'ahooks'
import { RocketLaunchIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import type { TableColumnsType } from 'antd'
import { Divider, Form, Input, Switch, Table } from 'antd'
import produce from 'immer'
import type { datasetInfoType } from '../index'
import type { KnowledgeEnhanceConfig } from '../knowledge-enhancement-setting'
import PreviewItem, { PreviewType } from './preview-item'
import s from './index.module.css'
import {
  AnalyType,
  DataSourceType, DatasetFileType,
  DocForm,

  IndexingType,
  SegmentType,
} from '@/models/datasets'

import cn from '@/utils/classnames'
import type {
  CreateDocumentReq,
  DataSet,
  FileIndexingEstimateResponse,
  FullDocumentDetail,
  IndexingEstimateParams,
  PreProcessingRule,
  ProcessRule,
  Rules,
  createDocumentResponse,
} from '@/models/datasets'
import {
  checkDocumentName,
  createDocument,
  createFirstDocument,
  fetchFileIndexingEstimate as didFetchFileIndexingEstimate,
  fetchDefaultProcessRule,
} from '@/service/datasets'
import { formatNumber } from '@/utils/format'

import { useDatasetDetailContext } from '@/context/dataset-detail'
import I18n from '@/context/i18n'
import { LanguagesSupported } from '@/i18n/language'
import { useProviderContext } from '@/context/provider-context'
import type { CustomFile, FileItem } from '@/types/public/file'

import style from '@/app/components/datasets/styles/style.module.css'
import {
  ensureRerankModelSelected,
  isReRankModelSelected,
} from '@/app/components/datasets/common/check-rerank-model'
import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import KnowledgeEnhancementSetting from '@/app/components/datasets/create/knowledge-enhancement-setting'
import {
  useDefaultModel,
  useModelList,
  useModelListAndDefaultModelAndCurrentProviderAndModel,
} from '@/app/components/account-setting/model-provider-page/hooks'
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
import type { DefaultModel } from '@/app/components/account-setting/model-provider-page/declarations'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import RadioCard from '@/app/components/base/radio-card'
import Scrollbar from '@/app/components/base/scrollbar'
import Toast from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import Loading from '@/app/components/base/loading'
import TextButton from '@/app/components/base/button/text-button'
import DropdownItem from '@/app/components/base/dropdown/dowdown-item'
import FileIcon from '@/app/components/base/file-icon'
import { fetchFileInfo } from '@/service/common'
import { RETRIEVE_METHOD, RerankingModeEnum, WeightedScoreEnum } from '@/types/datasets'

type ValueOf<T> = T[keyof T]
type StepTwoProps = {
  isSetting?: boolean
  documentDetail?: FullDocumentDetail
  isAPIKeySet: boolean
  datasetId?: string
  indexingType?: ValueOf<IndexingType>
  dataSourceType: DataSourceType
  datasetFormInfo: datasetInfoType
  files: CustomFile[]
  fileType?: DatasetFileType
  ready?: boolean
  updateIndexingTypeCache?: (type: string) => void
  updateResultCache?: (res: createDocumentResponse) => void
  updateDocumentParams?: (params: CreateDocumentReq) => void
  onSave?: (dataset?: DataSet) => void
  onCancel?: () => void
  fileList: FileItem[]
}

type DataTableItem = {
  name: string
  retrieval: boolean
}

const StepTwo = ({
  isSetting,
  documentDetail,
  isAPIKeySet,
  datasetId,
  indexingType,
  dataSourceType: inCreatePageDataSourceType,
  files,
  fileType,
  ready = true,
  updateIndexingTypeCache,
  updateResultCache,
  updateDocumentParams,
  onSave,
  onCancel,
  datasetFormInfo,
  fileList = [],
}: StepTwoProps) => {
  const { t } = useTranslation()
  const { locale } = useContext(I18n)
  // useXIYANRag 是否西研环境
  const { onPlanInfoChanged } = useProviderContext()
  const { dataset: currentDataset, mutateDatasetRes }
    = useDatasetDetailContext()
  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(
    ModelTypeEnum.rerank,
  )
  const {
    data: embeddingModelList,
    mutate: mutateEmbeddingModelList,
    isValidating: isEmbeddingModelListLoading,
  } = useModelList(ModelTypeEnum.textEmbedding)
  const { data: defaultEmbeddingModel } = useDefaultModel(
    ModelTypeEnum.textEmbedding,
  )
  // 是否西研环境
  const { useXIYANRag } = useProviderContext()

  // 是否正在创建的知识库
  const isInCreatePage
    = !datasetId || (datasetId && !currentDataset?.data_source_type)
  // 知识库数据类型
  const dataSourceType = isInCreatePage
    ? inCreatePageDataSourceType
    : currentDataset?.data_source_type
  // embeddingModel
  const [embeddingModel, setEmbeddingModel] = useState<DefaultModel>()
  // 分段设置
  const [segmentationType, setSegmentationType] = useState<SegmentType>(
    SegmentType.AUTO,
  )
  // 分段分隔符
  const [segmentIdentifier, setSegmentIdentifier] = useState('\\n')
  // 每段最大值
  const [max, setMax] = useState(10000) // default chunk length
  const [overlap, setOverlap] = useState(0)
  // 分段规则
  const [rules, setRules] = useState<PreProcessingRule[]>([])
  // 默认配置
  const [defaultConfig, setDefaultConfig] = useState<Rules>()
  // 索引方式
  const [indexType, setIndexType] = useState<ValueOf<IndexingType>>(
    (indexingType || isAPIKeySet)
      ? IndexingType.QUALIFIED
      : IndexingType.ECONOMICAL,
  )
  const [docForm, setDocForm] = useState<DocForm | string>(
    (datasetId && documentDetail) ? documentDetail.doc_form : DocForm.TEXT,
  )
  const [docLanguage, setDocLanguage] = useState<string>(
    (datasetId && documentDetail)
      ? documentDetail.doc_language
      : locale !== LanguagesSupported[1]
        ? 'English'
        : 'Chinese',
  )
  // 是否展开分段预览
  const [previewSwitched, setPreviewSwitched] = useState(false)
  // 是否显示预览按钮
  const [showPreview, { setTrue: setShowPreview, setFalse: hidePreview }]
    = useBoolean()
  // 看起来是自定义，自动分段的设置
  const [customFileIndexingEstimate, setCustomFileIndexingEstimate]
    = useState<FileIndexingEstimateResponse | null>(null)
  const [automaticFileIndexingEstimate, setAutomaticFileIndexingEstimate]
    = useState<FileIndexingEstimateResponse | null>(null)
  const fileIndexingEstimate = (() => {
    return segmentationType === SegmentType.AUTO
      ? automaticFileIndexingEstimate
      : customFileIndexingEstimate
  })()
  // 检索设置
  const [retrievalConfig, setRetrievalConfig] = useState(
    currentDataset?.retrieval_model_dict,
  )
  // 查看全部文档
  const [expandAll, setExpandAll] = useState(false)
  // 知识增强开关
  const [knowledgeEnhanceSet, setKnowledgeEnhanceSet] = useState<KnowledgeEnhanceConfig>({
    enhanced_enable: false,
    enhanced_feature: ['paragraphSummary', 'problemGeneration'],
    search_method: RETRIEVE_METHOD.semantic,
    reranking_enable: false,
    reranking_model: {
      reranking_provider_name: '',
      reranking_model_name: '',
    },
    top_k: 3,
    score_threshold: 0.5,
    score_threshold_enabled: false,
    reranking_mode: RerankingModeEnum.WeightedScore,
    weights: {
      weight_type: WeightedScoreEnum.SemanticFirst,
      vector_setting: {
        vector_weight: 1,
        embedding_provider_name: '',
        embedding_model_name: '',
      },
      keyword_setting: {
        keyword_weight: 0,
      },
    },
  })
  // 解析方式
  const [analysisType, setAnalysisType] = useState<AnalyType>(
    AnalyType.RAPID,
  )
  // 是否是结构化数据
  const isStructured = fileType === DatasetFileType.Structured
  // 是否可以开始结构化配置
  const isReadyToConfigStructuredData = useMemo(() => {
    return isStructured && files.length > 0 && files.every(file => file.id)
  }, [files, isStructured])
  // 文件数据配置
  const [fileDataConfigList, setFileDataConfigList] = useState<
  Array<{
    fileId: string
    sheets: Array<{
      name: string
      config: {
        header: number
        begin: number
        columns: Array<DataTableItem>
      }
      total_counts: number
    }>
  }>
  >([])
  // 当前文件id
  const [currFileId, setCurrFileId] = useState<string>()
  // 当前sheet
  const [currSheet, setCurrSheet] = useState<string>()
  // sheet列表
  const sheetList = useMemo(() => {
    if (isReadyToConfigStructuredData) {
      // 暂定第一个文件
      const currentDataConfig = fileDataConfigList.find(
        item => item.fileId === currFileId,
      )
      return (
        currentDataConfig?.sheets.map(sheet => ({
          label: sheet.name,
          value: sheet.name,
        })) || []
      )
    }
    return []
  }, [currFileId, fileDataConfigList, isReadyToConfigStructuredData])
  // 行列表
  const lineList = useMemo(() => {
    if (isReadyToConfigStructuredData) {
      // 暂定第一个文件
      const currentDataConfig = fileDataConfigList.find(
        item => item.fileId === currFileId,
      )
      const currentSheet = currentDataConfig?.sheets.find(
        sheet => sheet.name === currSheet,
      )
      const lines = []
      for (let i = 0; i < (currentSheet?.total_counts || 0); i++) {
        lines.push({
          label: t('common.info.row', { row: i + 1 }),
          value: i + 1,
        })
      }
      return lines
    }
    return []
  }, [
    currFileId,
    currSheet,
    fileDataConfigList,
    isReadyToConfigStructuredData,
    t,
  ])
  // 列信息
  const columnInfo = useMemo(() => {
    if (isReadyToConfigStructuredData) {
      const currentDataConfig = fileDataConfigList.find(
        item => item.fileId === currFileId,
      )
      const currentSheet = currentDataConfig?.sheets.find(
        sheet => sheet.name === currSheet,
      )
      return currentSheet?.config.columns || []
    }
    return []
  }, [
    currFileId,
    currSheet,
    fileDataConfigList,
    isReadyToConfigStructuredData,
  ])
  // 当前配置
  const currentConfig = useMemo(() => {
    if (isReadyToConfigStructuredData) {
      const currentDataConfig = fileDataConfigList.find(
        item => item.fileId === currFileId,
      )
      const currentSheet = currentDataConfig?.sheets.find(
        sheet => sheet.name === currSheet,
      )
      return currentSheet?.config
    }
  }, [
    currFileId,
    currSheet,
    fileDataConfigList,
    isReadyToConfigStructuredData,
  ])

  // 获取文件名
  const getFileName = (name: string) => {
    const arr = name.split('.')
    return arr.slice(0, -1).join('.')
  }
  // 获取规则名
  const getRuleName = (key: string) => {
    if (key === 'remove_extra_spaces')
      return t('datasetCreation.stepTwo.removeExtraSpaces')

    if (key === 'remove_urls_emails')
      return t('datasetCreation.stepTwo.removeUrlEmails')

    if (key === 'remove_stopwords')
      return t('datasetCreation.stepTwo.removeStopwords')
  }
  // 规则变换
  const ruleChangeHandle = (id: string) => {
    const newRules = rules.map((rule) => {
      if (rule.id === id) {
        return {
          id: rule.id,
          enabled: !rule.enabled,
        }
      }
      return rule
    })
    setRules(newRules)
  }
  const getIndexing_technique = () => indexingType || indexType
  const getProcessRule = () => {
    const processRule: ProcessRule = {
      rules: {} as any, // api will check this. It will be removed after api refactored.
      mode: segmentationType,
    }
    if (segmentationType === SegmentType.CUSTOM) {
      const ruleObj = {
        pre_processing_rules: rules,
        segmentation: {
          separator: segmentIdentifier === '\\n' ? '\n' : segmentIdentifier,
          max_tokens: max,
          chunk_overlap: overlap,
        },
      }
      processRule.rules = ruleObj
    }
    return processRule
  }
  // 获取文件分段预览接口的参数
  const getFileIndexingEstimateParams = (
    docForm: DocForm,
  ): IndexingEstimateParams | undefined => {
    if (dataSourceType === DataSourceType.FILE) {
      return {
        info_list: {
          data_source_type: dataSourceType,
          file_info_list: {
            file_ids: files.map(file => file.id) as string[],
          },
        },
        indexing_technique: getIndexing_technique() as string,
        process_rule: getProcessRule(),
        doc_form: docForm,
        doc_language: docLanguage,
        dataset_id: datasetId as string,
      }
    }
  }
  // 获取文件分段后的预览结果
  const fetchFileIndexingEstimate = async (docForm = DocForm.TEXT) => {
    if (useXIYANRag)
      return

    const res = await didFetchFileIndexingEstimate(
      getFileIndexingEstimateParams(docForm)!,
    )
    if (segmentationType === SegmentType.CUSTOM)
      setCustomFileIndexingEstimate(res)
    else setAutomaticFileIndexingEstimate(res)
  }
  // 增强内容
  const FEATURE_MAP = {
    paragraphSummary: '3',
    problemGeneration: '2',
  }
  const enhancedFeatureCheck = () => {
    const enhanced_feature_val = knowledgeEnhanceSet.enhanced_feature || []
    if (enhanced_feature_val?.length === 0) {
      Toast.notify({
        type: 'error',
        message: t('datasetCreation.stepTwo.enhancedFeatureTip'),
      })
      return []
    }
    const values: string[] = []
    if (knowledgeEnhanceSet.enhanced_enable) {
      Object.entries(FEATURE_MAP).forEach(([featureKey, featureValue]) => {
        if (enhanced_feature_val.includes(featureKey))
          values.push(featureValue)
      })
    }
    return values
  }

  const isFileList = () => {
    return fileList.some((item) => {
      const ext = item.file?.extension || ''
      return ext.toLowerCase() === 'pdf'
    })
  }
  // 获取创建接口参数
  const getCreationParams = (info?: datasetInfoType) => {
    const hasPdf = analysisType !== '1' ? true : isFileList()
    if (!hasPdf) {
      // 提示：上传文件需要有PDF格式的
      Toast.notify({
        type: 'error',
        message: t('datasetCreation.stepTwo.analyticMethod.analyticTip'),
      })

      return
    }

    let params
    if (segmentationType === SegmentType.CUSTOM && overlap > max) {
      Toast.notify({
        type: 'error',
        message: t('datasetCreation.stepTwo.overlapCheck'),
      })
      return
    }
    if (isStructured && fileDataConfigList.some(file => file.sheets.some(sheet => sheet.config.columns.every(column => !column.retrieval)))) {
      Toast.notify({
        type: 'error',
        message: t('datasetCreation.stepTwo.structuredDataCheck'),
      })
      return
    }
    if (isSetting) {
      params = {
        original_document_id: documentDetail?.id,
        doc_form: docForm,
        doc_language: docLanguage,
        process_rule: getProcessRule(),

        retrieval_model: retrievalConfig, // Readonly. If want to changed, just go to settings page.
        embedding_model: embeddingModel?.model, // Readonly
        embedding_model_provider: embeddingModel?.provider, // Readonly
      } as CreateDocumentReq
    }
    else {
      // create
      const indexMethod = getIndexing_technique()
      if (
        !isReRankModelSelected({
          rerankDefaultModel,
          isRerankDefaultModelValid: !!isRerankDefaultModelValid,
          rerankModelList,
          retrievalConfig: retrievalConfig!,
          indexMethod: indexMethod as string,
        })
      ) {
        Toast.notify({
          type: 'error',
          message: t('dataset.placeholder.rerankModel'),
        })
        return
      }
      const postRetrievalConfig = ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig: retrievalConfig!,
        indexMethod: indexMethod as string,
      })
      params = {
        name: info ? info.name : datasetFormInfo.name,
        description: info ? info.description : datasetFormInfo.description,
        data_source: {
          type: dataSourceType,
          info_list: {
            data_source_type: dataSourceType,
          },
        },
        indexing_technique: getIndexing_technique(),
        process_rule: getProcessRule(),
        doc_form: docForm,
        doc_language: docLanguage,

        retrieval_model: postRetrievalConfig,
        embedding_model: embeddingModel?.model,
        embedding_model_provider: embeddingModel?.provider,
        // 补充数据配置
        file_data_config: fileDataConfigList,
        ocr_enable: analysisType, // 解析方式
      } as CreateDocumentReq
      if (!datasetId) {
        const enhancedFeatureList = enhancedFeatureCheck()
        if (knowledgeEnhanceSet.enhanced_enable && enhancedFeatureList.length === 0) {
          // 检测到开启增强功能，未选中增强内容，直接返回
          return
        }
        params = {
          ...params,
          // 知识增强
          enhanced_enable: knowledgeEnhanceSet.enhanced_enable ? '1' : '0',
          enhanced_feature: enhancedFeatureList,
          // 大模型
          // knowledge_model: knowledgeEnhanceSet.enhanced_enable ? knowledgeEnhanceSet.knowledge_model : {},
        }
      }
      console.log(knowledgeEnhanceSet, '====知识增强===')
      console.log('===创建参数处理===', params)
      if (dataSourceType === DataSourceType.FILE) {
        params.data_source.info_list.file_info_list = {
          file_ids: files.map(file => file.id || '').filter(Boolean),
        }
      }
      // if (dataSourceType === DataSourceType.NOTION)
      //   params.data_source.info_list.notion_info_list = getNotionInfo()

      // if (dataSourceType === DataSourceType.WEB)
      //   params.data_source.info_list.website_info_list = getWebsiteInfo()
    }
    if (updateDocumentParams)
      updateDocumentParams(params)
    return params
  }
  // 获取默认规则——创建阶段
  const getRules = async () => {
    try {
      const res = await fetchDefaultProcessRule({
        url: '/datasets/process-rule',
      })
      const separator = res.rules.segmentation.separator
      setSegmentIdentifier((separator === '\n' ? '\\n' : separator) || '\\n')
      setMax(res.rules.segmentation.max_tokens)
      setOverlap(res.rules.segmentation.chunk_overlap)
      setRules(res.rules.pre_processing_rules)
      setDefaultConfig(res.rules)
    }
    catch (err) {
      // console.log(err)
    }
  }
  // 获取默认规则——编辑阶段
  const getRulesFromDetail = () => {
    if (documentDetail) {
      const rules = documentDetail.dataset_process_rule.rules
      const separator = rules.segmentation.separator
      const max = rules.segmentation.max_tokens
      const overlap = rules.segmentation.chunk_overlap
      setSegmentIdentifier((separator === '\n' ? '\\n' : separator) || '\\n')
      setMax(max)
      setOverlap(overlap)
      setRules(rules.pre_processing_rules)
      setDefaultConfig(rules)
    }
  }
  // 重置当前规则
  const resetRules = () => {
    if (defaultConfig) {
      setSegmentIdentifier(
        (defaultConfig.segmentation.separator === '\n'
          ? '\\n'
          : defaultConfig.segmentation.separator) || '\\n',
      )
      setMax(defaultConfig.segmentation.max_tokens)
      setOverlap(defaultConfig.segmentation.chunk_overlap)
      setRules(defaultConfig.pre_processing_rules)
    }
  }
  // 获取默认分段设置
  const getDefaultMode = () => {
    if (documentDetail)
      setSegmentationType(documentDetail.dataset_process_rule.mode)
  }
  // 确认使用当前自定义配置
  const confirmChangeCustomConfig = () => {
    setCustomFileIndexingEstimate(null)
    setShowPreview()
    fetchFileIndexingEstimate()
    setPreviewSwitched(false)
  }
  // 变更展开全部文档
  const onOpenChange = (open: boolean) => {
    setExpandAll(open)
  }
  // 变更数据配置
  const changeDataSelectConfig = (key: string, value: any) => {
    if (key === 'sheet') {
      // todo 联动行数组
      setCurrSheet(value)
    }
    else if (key === 'header') {
      const inputs = produce(fileDataConfigList, (draft) => {
        draft.forEach((item) => {
          if (item.fileId === currFileId) {
            item.sheets.forEach((sheet) => {
              if (sheet.name === currSheet) {
                sheet.config.header = value
                sheet.config.begin
                  = value + 1 > lineList.length ? lineList.length : value + 1
              }
            })
          }
        })
      })
      setFileDataConfigList(inputs)
    }
    else if (key === 'begin') {
      const inputs = produce(fileDataConfigList, (draft) => {
        draft.forEach((item) => {
          if (item.fileId === currFileId) {
            item.sheets.forEach((sheet) => {
              if (sheet.name === currSheet)
                sheet.config.begin = value
            })
          }
        })
      })
      setFileDataConfigList(inputs)
    }
  }
  // 变更列配置
  const changeColumnConfig = (key: string, value: any) => {
    const inputs = produce(fileDataConfigList, (draft) => {
      draft.forEach((item) => {
        if (item.fileId === currFileId) {
          item.sheets.forEach((sheet) => {
            if (sheet.name === currSheet) {
              sheet.config.columns.forEach((column) => {
                if (column.name === key)
                  column.retrieval = value
              })
            }
          })
        }
      })
    })
    setFileDataConfigList(inputs)
  }

  // 第二步完成
  const createHandle = async () => {
    try {
      let res
      const params = getCreationParams(datasetFormInfo)
      console.log(params, '====params===')
      // return false

      if (!params)
        return false

      if (!datasetId) {
        // 判断知识库名称是否重复
        const exist = await checkDocumentName({ name: datasetFormInfo.name })
        if (exist) {
          Toast.notify({
            type: 'error',
            message: t('dataset.notify.nameRepeat'),
          })
          return false
        }
        res = await createFirstDocument({
          body: params as CreateDocumentReq,
          params: {
            category: isStructured ? DatasetFileType.Structured : DatasetFileType.Unstructured,
          },
        })
        // 知识库限制数量
        onPlanInfoChanged()
        updateIndexingTypeCache && updateIndexingTypeCache(indexType as string)
        updateResultCache && updateResultCache(res)
      }
      else {
        res = await createDocument({
          datasetId,
          body: params as CreateDocumentReq,
          params: {
            category: isStructured ? DatasetFileType.Structured : DatasetFileType.Unstructured,
          },
        })
        updateIndexingTypeCache && updateIndexingTypeCache(indexType as string)
        updateResultCache && updateResultCache(res)
      }
      if (mutateDatasetRes)
        mutateDatasetRes()
      onSave && onSave(res.dataset)
    }
    catch (err) {}
  }

  useEffect(() => {
    if (indexingType === IndexingType.ECONOMICAL && docForm === DocForm.QA)
      setDocForm(DocForm.TEXT)
  }, [indexingType, docForm])
  // 根据当前知识库状况
  useEffect(() => {
    if (!isSetting) {
      getRules()
    }
    else {
      getRulesFromDetail()
      getDefaultMode()
    }
  }, [])
  // 索引类型初始化
  useEffect(() => {
    if (indexingType) {
      setIndexType(indexingType as IndexingType)
    }
    else {
      setIndexType(
        isAPIKeySet ? IndexingType.QUALIFIED : IndexingType.ECONOMICAL,
      )
    }
  }, [isAPIKeySet, indexingType, datasetId])
  // 分段设置变更
  useDebounceEffect(
    () => {
      if (segmentationType === SegmentType.AUTO) {
        setAutomaticFileIndexingEstimate(null)
        setShowPreview()
        fetchFileIndexingEstimate()
        setPreviewSwitched(false)
      }
      else {
        setShowPreview()
        confirmChangeCustomConfig()
        setCustomFileIndexingEstimate(null)
        setPreviewSwitched(false)
      }
    },
    [segmentationType, indexType],
    {
      wait: 100,
    },
  )
  // 初始化 embeddingModel
  useEffect(() => {
    const initialModel = currentDataset?.embedding_model
      ? {
        provider: currentDataset.embedding_model_provider,
        model: currentDataset.embedding_model,
      }
      : defaultEmbeddingModel
        ? {
          provider: defaultEmbeddingModel.provider.provider,
          model: defaultEmbeddingModel.model,
        }
        : undefined

    if (initialModel) {
      setEmbeddingModel(initialModel)
      // 同步更新知识增强设置中的模型信息
      setKnowledgeEnhanceSet(prev => ({
        ...prev,
        weights: {
          ...prev.weights!,
          vector_setting: {
            ...prev.weights!.vector_setting,
            embedding_provider_name: initialModel.provider,
            embedding_model_name: initialModel.model,
          },
        },
      }))
    }
  }, [currentDataset, defaultEmbeddingModel])
  // 根据文件id初始化文件配置
  useAsyncEffect(async () => {
    if (isReadyToConfigStructuredData) {
      try {
        const { metadatas: result } = await fetchFileInfo(
          files.map(file => file.id!),
        )
        if (result.length > 0) {
          setFileDataConfigList(
            result.map(item => ({
              fileId: item.id as unknown as string,
              // 暂时只放入一个sheet
              sheets: item.metadata.length
                ? [{
                  name: item.metadata[0].sheet_name,
                  config: {
                    header: 1,
                    begin: 2,
                    columns: item.metadata[0].columns.map(column => ({
                      name: column,
                      retrieval: true,
                    })),
                  },
                  total_counts: item.metadata[0].total_count,
                }]
                : [],
            })),
          )
          setCurrFileId(result[0].id as unknown as string)
          setCurrSheet(result[0].metadata[0].sheet_name)
        }
      }
      catch (err) {
        setFileDataConfigList([])
      }
    }
    else {
      setFileDataConfigList([])
    }
  }, [files, isReadyToConfigStructuredData])

  // 数据表格列
  const columns: TableColumnsType<DataTableItem> = [
    {
      key: 'name',
      dataIndex: 'name',
      title: t('datasetCreation.stepTwo.form.col'),
    },
    {
      key: 'canIndex',
      title: t('datasetCreation.stepTwo.form.canIndex'),
      render: (_: any, record: DataTableItem) => {
        return <Switch defaultValue={record.retrieval} onChange={value => changeColumnConfig(record.name, value)}></Switch>
      },
    }, /* {    key: 'operation',
      title: t('common.info.operation'),
      render: (_: any, record: DataTableItem) => {
        return (
          <TextButton variant='primary'>{t('common.operation.delete')}</TextButton>
        )
      },
    } */
  ]

  if (isSetting) {
    return (
      <div className={style.wrap}>
        <div className={style['left-part']}>
          {/* 头部标题及隐藏按钮 */}
          <div className={cn(style['left-title'], 'justify-between')}>
            <span>{t('datasetCreation.stepTwo.configSelect')}</span>
            <Button
              className={cn(showPreview ? '!hidden' : '')}
              variant={'secondary-accent'}
              onClick={setShowPreview}
            >
              <RocketLaunchIcon className="h-4 w-4 mr-1.5 stroke-[1.8px]" />
              <span>{t('common.operation.preview')}</span>
            </Button>
          </div>
          <Scrollbar className={cn(style['left-content'])}>
            <Form layout="vertical">
              {/* 分段设置 */}
              <Form.Item label={t('datasetCreation.stepTwo.segmentation')}>
                <RadioCard
                  className="mb-2"
                  title={t('datasetCreation.stepTwo.auto')}
                  description={t('datasetCreation.stepTwo.autoDescription')}
                  isChosen={segmentationType === SegmentType.AUTO}
                  onChosen={() => setSegmentationType(SegmentType.AUTO)}
                ></RadioCard>
                <RadioCard
                  title={t('datasetCreation.stepTwo.custom')}
                  description={t('datasetCreation.stepTwo.customDescription')}
                  isChosen={segmentationType === SegmentType.CUSTOM}
                  onChosen={() => setSegmentationType(SegmentType.CUSTOM)}
                  chosenConfig={
                    <Form layout="vertical">
                      <Form.Item label={t('datasetCreation.stepTwo.separator')}>
                        <Input
                          placeholder={
                            t('datasetCreation.stepTwo.separatorPlaceholder')
                            || ''
                          }
                          value={segmentIdentifier}
                          onChange={e => setSegmentIdentifier(e.target.value)}
                        ></Input>
                      </Form.Item>
                      <Form.Item
                        label={t('datasetCreation.stepTwo.maxLength')}
                        tooltip={t('datasetCreation.stepTwo.maxLengthTip')}
                      >
                        <Input
                          type="number"
                          placeholder={
                            t('datasetCreation.stepTwo.maxLength') || ''
                          }
                          value={max}
                          min={500}
                          onChange={e =>
                            setMax(
                              parseInt(e.target.value.replace(/^0+/, ''), 10),
                            )
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        label={t('datasetCreation.stepTwo.overlap')}
                        tooltip={t('datasetCreation.stepTwo.overlapTip')}
                      >
                        <Input
                          type="number"
                          placeholder={
                            t('datasetCreation.stepTwo.overlap') || ''
                          }
                          value={overlap}
                          min={0}
                          onChange={(e) => {
                            const value = e.target.value
                            // 如果输入的是单个 0，保留它
                            if (value === '0') {
                              setOverlap(0)
                              return
                            }
                            // 否则去除前导零
                            setOverlap(parseInt(value.replace(/^0+/, ''), 10))
                          }}
                        />
                      </Form.Item>
                      <Form.Item label={t('datasetCreation.stepTwo.rules')}>
                        {rules.map(rule => (
                          <div key={rule.id} className={s.ruleItem}>
                            <input
                              id={rule.id}
                              type="checkbox"
                              checked={rule.enabled}
                              onChange={() => ruleChangeHandle(rule.id)}
                              className="w-4 h-4 rounded border-gray-300 text-blue-700 focus:ring-blue-700"
                            />
                            <label
                              htmlFor={rule.id}
                              className="ml-2 text-sm font-normal cursor-pointer text-gray-G1 leading-[24px]"
                            >
                              {getRuleName(rule.id)}
                            </label>
                          </div>
                        ))}
                      </Form.Item>
                      <div>
                        <Button
                          variant="primary"
                          className={'mr-4'}
                          onClick={confirmChangeCustomConfig}
                        >
                          {t('datasetCreation.stepTwo.preview')}
                        </Button>
                        <Button
                          variant={'secondary-accent'}
                          onClick={resetRules}
                        >
                          {t('common.operation.reset')}
                        </Button>
                      </div>
                    </Form>
                  }
                ></RadioCard>
              </Form.Item>
              {/* 检索设置 */}
              <Form.Item label={t('dataset.action.setting')}>
                <RetrievalMethodConfig
                  value={retrievalConfig}
                  onChange={setRetrievalConfig}
                />
              </Form.Item>
              {/* 文件预览 */}
              {false && (
                <div className={s.source}>
                  <div className={s.sourceContent}>
                    {dataSourceType === DataSourceType.FILE && (
                      <>
                        <div className="flex justify-between mb-2">
                          <div className="text-S3 leading-H4">
                            {t('datasetCreation.stepTwo.fileSource')}
                          </div>
                          {files.length > 1 && (
                            <TextButton
                              size="middle"
                              variant="primary"
                              onClick={() => onOpenChange(!expandAll)}
                            >
                              {expandAll
                                ? t('datasetCreation.stepTwo.repackUp')
                                : t('datasetCreation.stepTwo.viewAll')}
                              <ArrowDown
                                className={expandAll ? 'rotate-180' : ''}
                              />
                            </TextButton>
                          )}
                        </div>
                        {(!expandAll && files.length)
                          ? (
                            <DropdownItem
                              className="!px-2 !py-[2px]"
                              icon={
                                <FileIcon
                                  className={s.fileIcon}
                                  type={
                                    files.length ? (files[0].extension || '') : ''
                                  }
                                />
                              }
                              title={getFileName(files[0].name || '')}
                              onClick={() => {}}
                            ></DropdownItem>
                          )
                          : (
                            <Scrollbar className="h-[150px]">
                              {files.map(file => (
                                <DropdownItem
                                  className="!px-2 !py-[2px]"
                                  key={file.id}
                                  icon={
                                    <FileIcon
                                      className={s.fileIcon}
                                      type={
                                        files.length
                                          ? (files[0].extension || '')
                                          : ''
                                      }
                                    />
                                  }
                                  title={getFileName(file.name || '')}
                                  onClick={() => {}}
                                ></DropdownItem>
                              ))}
                            </Scrollbar>
                          )}
                      </>
                    )}
                  </div>
                  <Divider className="!mx-3 h-full" type="vertical"></Divider>
                  <div className={s.segmentCount}>
                    <div className="mb-2 text-S3 leading-H4 font-semibold">
                      {t('datasetCreation.stepTwo.estimateSegment')}
                    </div>
                    <div className="flex items-center text-S4 leading-H4">
                      {fileIndexingEstimate
                        ? (
                          <div className="font-semibold text-gray-G1">
                            {formatNumber(fileIndexingEstimate!.total_segments)}{' '}
                          </div>
                        )
                        : (
                          <div className={s.calculating}>
                            {t('datasetCreation.stepTwo.calculating')}
                          </div>
                        )}
                    </div>
                  </div>
                </div>
              )}
              {/* 按钮组 */}
              <div className="flex items-center mt-6">
                <Button
                  size="large"
                  variant={'secondary-accent'}
                  className="mr-4"
                  onClick={onCancel}
                >
                  {t('common.operation.cancel')}
                </Button>
                <Button size="large" variant="primary" onClick={createHandle}>
                  {t('common.operation.save')}
                </Button>
              </div>
            </Form>
          </Scrollbar>
        </div>
        <div className={style['right-part']}>
          <div className={style['right-title']}>
            {t('datasetCreation.stepTwo.previewContent')}
          </div>
          <Scrollbar
            className={cn(style['right-content'], 'flex flex-col gap-3')}
          >
            {previewSwitched
              && docForm === DocForm.QA
              && fileIndexingEstimate?.qa_preview && (
              <>
                {fileIndexingEstimate?.qa_preview.map((item, index) => (
                  <PreviewItem
                    type={PreviewType.QA}
                    key={item.question}
                    qa={item}
                    index={index + 1}
                  />
                ))}
              </>
            )}
            {(docForm === DocForm.TEXT || !previewSwitched)
              && fileIndexingEstimate?.preview && (
              <>
                {fileIndexingEstimate?.preview.map((item, index) => (
                  <PreviewItem
                    type={PreviewType.TEXT}
                    key={item}
                    content={item}
                    index={index + 1}
                  />
                ))}
              </>
            )}
            {previewSwitched
              && docForm === DocForm.QA
              && !fileIndexingEstimate?.qa_preview && (
              <div className="flex items-center justify-center h-[200px]">
                <Loading type="area" />
              </div>
            )}
            {!previewSwitched && !fileIndexingEstimate?.preview && (
              <div className="flex items-center justify-center h-[200px]">
                <Loading type="area" />
              </div>
            )}
          </Scrollbar>
        </div>
      </div>
    )
  }
  else {
    return (
      <Form layout="vertical">
        {/* 分段设置 */}
        <Form.Item label={t('datasetCreation.stepTwo.segmentation')}>
          <RadioCard
            className="mb-2"
            title={t('datasetCreation.stepTwo.auto')}
            description={t('datasetCreation.stepTwo.autoDescription')}
            isChosen={segmentationType === SegmentType.AUTO}
            onChosen={() => setSegmentationType(SegmentType.AUTO)}
          ></RadioCard>
          <RadioCard
            title={t('datasetCreation.stepTwo.custom')}
            description={t('datasetCreation.stepTwo.customDescription')}
            isChosen={segmentationType === SegmentType.CUSTOM}
            onChosen={() => setSegmentationType(SegmentType.CUSTOM)}
            chosenConfig={
              <Form layout="vertical">
                <Form.Item label={t('datasetCreation.stepTwo.separator')}>
                  <Input
                    placeholder={
                      t('datasetCreation.stepTwo.separatorPlaceholder') || ''
                    }
                    value={segmentIdentifier}
                    onChange={e => setSegmentIdentifier(e.target.value)}
                  ></Input>
                </Form.Item>
                <Form.Item
                  label={t('datasetCreation.stepTwo.maxLength')}
                  tooltip={t('datasetCreation.stepTwo.maxLengthTip')}
                >
                  <Input
                    type="number"
                    placeholder={t('datasetCreation.stepTwo.maxLength') || ''}
                    value={max}
                    min={500}
                    onChange={e =>
                      setMax(parseInt(e.target.value.replace(/^0+/, ''), 10))
                    }
                  />
                </Form.Item>
                <Form.Item
                  label={t('datasetCreation.stepTwo.overlap')}
                  tooltip={t('datasetCreation.stepTwo.overlapTip')}
                >
                  <Input
                    type="number"
                    placeholder={t('datasetCreation.stepTwo.overlap') || ''}
                    value={overlap}
                    min={0}
                    onChange={(e) => {
                      const value = e.target.value
                      // 如果输入的是单个 0，保留它
                      if (value === '0') {
                        setOverlap(0)
                        return
                      }
                      // 否则去除前导零
                      setOverlap(parseInt(value.replace(/^0+/, ''), 10))
                    }}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  label={t('datasetCreation.stepTwo.rules')}
                >
                  {rules.map(rule => (
                    <div key={rule.id} className={s.ruleItem}>
                      <input
                        id={rule.id}
                        type="checkbox"
                        checked={rule.enabled}
                        onChange={() => ruleChangeHandle(rule.id)}
                        className="w-4 h-4 rounded border-gray-300 text-blue-700 focus:ring-blue-700"
                      />
                      <label
                        htmlFor={rule.id}
                        className="ml-2 text-sm font-normal cursor-pointer text-gray-G1 leading-[24px]"
                      >
                        {getRuleName(rule.id)}
                      </label>
                    </div>
                  ))}
                </Form.Item>
              </Form>
            }
          ></RadioCard>
        </Form.Item>
        {/* Embedding model */}
        {indexType === IndexingType.QUALIFIED && !datasetId && (
          <Form.Item label={t('dataset.info.embeddingModel')}>
            <ModelSelector
              loading={isEmbeddingModelListLoading}
              readonly={!!datasetId}
              defaultModel={embeddingModel}
              modelList={embeddingModelList}
              onSelect={(model: DefaultModel) => {
                setEmbeddingModel(model)
                // 同步更新知识增强设置中的模型信息
                setKnowledgeEnhanceSet(prev => ({
                  ...prev,
                  weights: {
                    ...prev.weights!,
                    vector_setting: {
                      ...prev.weights!.vector_setting,
                      embedding_provider_name: model.provider,
                      embedding_model_name: model.model,
                    },
                  },
                }))
              }}
              triggerClassName="h-[40px]"
              onFetch={mutateEmbeddingModelList}
            />
            {!!datasetId && (
              <div className="mt-2 desc-14">
                {t('datasetCreation.stepTwo.indexSettingTip')}
                <Link
                  className="text-primary-P1"
                  href={`/datasets/${datasetId}/settings`}
                >
                  {t('datasetCreation.stepTwo.datasetSettingLink')}
                </Link>
              </div>
            )}
          </Form.Item>
        )}
        {/* 检索设置 */}
        <Form.Item label={t('datasetCreation.stepTwo.setting')}>
          <RetrievalMethodConfig
            value={retrievalConfig}
            onChange={setRetrievalConfig}
          />
        </Form.Item>
        {/* 结构化数据 */}
        {isReadyToConfigStructuredData && (
          <>
            {/* 数据选择 */}
            <div className="text-S4 leading-H4 text-gray-G1 mb-4 font-semibold">
              {t('datasetCreation.stepTwo.dataSelect')}
            </div>
            {/* <Form.Item>
              <Row gutter={12}>
                <Col span={8}>
                  <Form.Item className="!mb-0" label={t("datasetCreation.stepTwo.form.sheet")}>
                    <Select
                      value={currSheet}
                      options={sheetList}
                      onChange={(value) =>
                        changeDataSelectConfig("sheet", value)
                      }
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item className="!mb-0" label={t("datasetCreation.stepTwo.form.header")}>
                    <Select
                      value={currentConfig?.header}
                      options={lineList}
                      onChange={(value) =>
                        changeDataSelectConfig("header", value)
                      }
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    className="!mb-0"
                    label={t("datasetCreation.stepTwo.form.beginLine")}
                  >
                    <Select
                      value={currentConfig?.begin}
                      options={lineList}
                      onChange={(value) =>
                        changeDataSelectConfig("begin", value)
                      }
                    ></Select>
                  </Form.Item>
                </Col>
              </Row>
            </Form.Item> */}
            {/* 数据表 */}
            <Form.Item label={t('datasetCreation.stepTwo.table')}>
              <Table
                pagination={false}
                columns={columns}
                dataSource={columnInfo}
                size="middle"
                className="border-gray-G6 rounded border"
              ></Table>
            </Form.Item>
          </>
        )}
        {/* 解析方式 */}
        {
          useXIYANRag && !isStructured && (
            <Form.Item label={t('datasetCreation.stepTwo.analyticMethod.title')}>
              <RadioCard
                className="mb-2"
                title={t('datasetCreation.stepTwo.analyticMethod.rapidAnalysis')}
                description={t('datasetCreation.stepTwo.analyticMethod.rapidDescription')}
                isChosen={analysisType === AnalyType.RAPID}
                onChosen={() => setAnalysisType(AnalyType.RAPID)}
              ></RadioCard>
              <RadioCard
                title={t('datasetCreation.stepTwo.analyticMethod.accurateAnalysis')}
                description={t('datasetCreation.stepTwo.analyticMethod.accurateDescription')}
                isChosen={analysisType === AnalyType.ACCURATE}
                onChosen={() => setAnalysisType(AnalyType.ACCURATE)}
              ></RadioCard>
            </Form.Item>
          )
        }
        {/* 创建知识库-知识增强 */}
        {
          useXIYANRag && !datasetId && !isStructured && (
            <Form.Item label={t('datasetCreation.stepTwo.knowledgeEnhancement.title')}>
              <KnowledgeEnhancementSetting
                value={knowledgeEnhanceSet}
                onChange={(value: KnowledgeEnhanceConfig) => setKnowledgeEnhanceSet(value)}
              />
            </Form.Item>
          )
        }

        {/* 按钮组 */}
        <div className="flex items-center mt-6">
          <Button
            size="large"
            disabled={!ready || (isStructured && !fileDataConfigList.length)}
            variant="primary"
            onClick={createHandle}
          >
            {t('common.operation.save')}
          </Button>
          {/* <Button
            size="large"
            variant="primary"
            onClick={createHandle}
          >
            测试用
          </Button> */}
        </div>
      </Form>
    )
  }
}

export default StepTwo
